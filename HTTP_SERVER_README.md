# MCP HTTP 服务器

这个项目现在支持通过 HTTP 接口提供 MCP (Model Context Protocol) 服务。

## 启动 HTTP 服务器

### 开发模式
```bash
npm run dev:http
```

### 生产模式
```bash
npm run build
npm run start:http
```

## 配置

在 `.env` 文件中配置：

```env
# HTTP服务器端口
PORT=3000

# MCP调试配置
DEBUG=true
NODE_ENV=development

# Figma API Key
FIGMA_API_KEY=your_figma_api_key_here
```

## API 端点

### POST /mcp
主要的 MCP 通信端点，支持所有 MCP 协议操作。

**请求头要求：**
- `Content-Type: application/json`
- `Accept: application/json, text/event-stream`
- `mcp-session-id: <session-id>` (除了初始化请求)

### GET /mcp
用于服务器到客户端的通知流（需要 session ID）

### DELETE /mcp
用于终止会话（需要 session ID）

## 使用示例

### 1. 初始化连接
```javascript
const initRequest = {
  jsonrpc: "2.0",
  id: 1,
  method: "initialize",
  params: {
    protocolVersion: "2025-03-26",
    capabilities: { tools: {} },
    clientInfo: { name: "test-client", version: "1.0.0" }
  }
};

// POST 到 /mcp，响应会包含 mcp-session-id 头
```

### 2. 列出可用工具
```javascript
const toolsRequest = {
  jsonrpc: "2.0",
  id: 2,
  method: "tools/list",
  params: {}
};

// 使用从初始化响应中获得的 session ID
```

### 3. 调用工具
```javascript
const toolCallRequest = {
  jsonrpc: "2.0",
  id: 3,
  method: "tools/call",
  params: {
    name: "get-system-info",
    arguments: {}
  }
};
```

## 可用工具

- **get-system-info**: 获取系统基本信息
- **list-allowed-commands**: 显示允许和禁止的命令列表
- **get_figma_data**: 获取 Figma 文件的布局信息
- **download_figma_images**: 下载 Figma 文件中的 SVG 和 PNG 图像

## 响应格式

服务器使用 Server-Sent Events (SSE) 格式返回响应：

```
event: message
data: {"result": {...}, "jsonrpc": "2.0", "id": 1}
```

## 测试

项目包含了测试客户端文件：
- `test-http-client.js` - 测试初始化连接
- `test-tools.js` - 测试工具列表
- `test-tool-call.js` - 测试工具调用

运行测试：
```bash
node test-http-client.js
node test-tools.js
node test-tool-call.js
```

## 技术实现

- 使用 Express.js 作为 HTTP 服务器
- 使用 `@modelcontextprotocol/sdk` 的 `StreamableHTTPServerTransport`
- 支持会话管理和状态保持
- 支持 SSE 流式响应
- 完全兼容 MCP 协议规范
